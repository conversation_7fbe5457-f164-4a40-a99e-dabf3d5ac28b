#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>


namespace at {
namespace native {
TORCH_API at::Tensor float_power(const at::Tensor & self, const at::Tensor & exponent);
TORCH_API at::Tensor & float_power_out(const at::Tensor & self, const at::Tensor & exponent, at::Tensor & out);
TORCH_API at::Tensor & float_power_(at::Tensor & self, const at::Tensor & exponent);
TORCH_API at::Tensor float_power(const at::Scalar & self, const at::Tensor & exponent);
TORCH_API at::Tensor & float_power_out(const at::Scalar & self, const at::Tensor & exponent, at::Tensor & out);
TORCH_API at::Tensor float_power(const at::Tensor & self, const at::Scalar & exponent);
TORCH_API at::Tensor & float_power_out(const at::Tensor & self, const at::Scalar & exponent, at::Tensor & out);
TORCH_API at::Tensor & float_power_(at::Tensor & self, const at::Scalar & exponent);
} // namespace native
} // namespace at
