#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/expm1_ops.h>

namespace at {


// aten::expm1(Tensor self) -> Tensor
inline at::Tensor expm1(const at::Tensor & self) {
    return at::_ops::expm1::call(self);
}

// aten::expm1_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & expm1_(at::Tensor & self) {
    return at::_ops::expm1_::call(self);
}

// aten::expm1.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & expm1_out(at::Tensor & out, const at::Tensor & self) {
    return at::_ops::expm1_out::call(self, out);
}
// aten::expm1.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & expm1_outf(const at::Tensor & self, at::Tensor & out) {
    return at::_ops::expm1_out::call(self, out);
}

}
