"""
Finální, robustní pipeline pro Optuna-based hyperparameter optimalizaci.
Verze 6.7 - Kompletní skript s opravenou 'optimize' metodou a správným XGBoost API.
"""
from datetime import datetime
from tqdm import tqdm
import numpy as np
import pickle
import joblib
import optuna
import time
import json
import logging
import sys
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# ML knihovny
from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import mean_absolute_error
from sklearn.base import BaseEstimator, TransformerMixin
import psutil
import xgboost as xgb

# Import extraktoru
try:
    sys.path.append('training_scripts')
    from feature_engineering_v2 import FeatureExtractorV2
except ImportError:
    print("CHYBA: Ujistěte se, že máte soubor 'training_scripts/feature_engineering_v2.py'.")
    sys.exit(1)

def setup_logging():
    log_dir = Path("logs"); log_dir.mkdir(exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"optuna_xgboost_run_{timestamp}.log"
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s',
                        handlers=[logging.FileHandler(log_file), logging.StreamHandler(sys.stdout)])
    return logging.getLogger(__name__)

class Config:
    DATA_PATH = '/home/<USER>/Loziska/AMSM_LOZISKA_2025/student_data.pkl'
    RESULTS_DIR = Path('optuna_results_final_v2')
    N_TRIALS_PER_MODEL = 100
    CV_FOLDS = 5
    RANDOM_STATE = 42
    N_JOBS_OPTUNA = 8
    N_JOBS_EXTRACTION = 16
    NUM_SYNTHETIC_SAMPLES = 40000

class SignalAugmenter:
    """Fyzikálně informovaná augmentace na úrovni surových signálů."""
    def __init__(self, n_synthetic, discrete_levels=None, noise_level=0.01):
        self.n_synthetic, self.discrete_levels, self.noise_level = n_synthetic, discrete_levels or [2500, 7500, 12500, 17500], noise_level
    def fit_resample(self, X, y):
        logging.info(f"Generuji {self.n_synthetic} syntetických signálů...")
        X_synth, y_synth = [], []
        level_indices = {level: np.where(y == level)[0] for level in self.discrete_levels}
        for _ in tqdm(range(self.n_synthetic), desc="Generování signálů"):
            level_idx = np.random.randint(0, len(self.discrete_levels) - 1)
            level1, level2 = self.discrete_levels[level_idx], self.discrete_levels[level_idx+1]
            idx1, idx2 = np.random.choice(level_indices[level1]), np.random.choice(level_indices[level2])
            signal1, signal2 = X[idx1], X[idx2]
            alpha = np.random.rand()
            new_signal = (1 - alpha) * signal1 + alpha * signal2
            new_y = (1 - alpha) * level1 + alpha * level2
            noise = np.random.normal(0, self.noise_level * np.std(new_signal), new_signal.shape)
            X_synth.append(new_signal + noise); y_synth.append(new_y)
        X_final = np.vstack([X, np.array(X_synth)])
        y_final = np.concatenate([y, np.array(y_synth)])
        logging.info(f"Augmentace dokončena. Celkem {len(X_final)} vzorků.")
        return X_final, y_final

def save_best_model_callback(study: optuna.study.Study, trial: optuna.trial.FrozenTrial):
    """Callback pro ukládání nejlepších parametrů po každém úspěšném trialu."""
    if study.best_trial.number == trial.number:
        logger = logging.getLogger(__name__)
        model_name = study.study_name.replace("_study", "")
        output_path = Path('optuna_results_final_v2') / f'best_params_{model_name}.json'
        best_info = {'mae': trial.value, 'params': trial.params, 'trial_number': trial.number}
        with open(output_path, 'w') as f: json.dump(best_info, f, indent=4)
        logger.info(f"Nový nejlepší model pro '{model_name}' nalezen! MAE: {trial.value:.2f}. Uloženo do {output_path}")

class OptunaOptimizer:
    def __init__(self, config: Config):
        self.config = config
        self.feature_extractor = FeatureExtractorV2(n_jobs=self.config.N_JOBS_EXTRACTION)
        self.best_params, self.best_scores = {}, {}
        self._load_and_prepare_data()

    def _load_and_prepare_data(self):
        logging.info("Načítání a příprava dat...")
        self.config.RESULTS_DIR.mkdir(exist_ok=True)
        with open(self.config.DATA_PATH, 'rb') as f: data = pickle.load(f)
        X_train_raw, y_train_raw = data['train_dataset'], data['train_response']
        augmenter = SignalAugmenter(n_synthetic=self.config.NUM_SYNTHETIC_SAMPLES)
        X_aug_raw, self.y_train = augmenter.fit_resample(X_train_raw, y_train_raw)
        logging.info("Extrakce V2 příznaků z augmentovaných dat...")
        X_features = self.feature_extractor.transform(X_aug_raw)
        scaler = RobustScaler()
        self.X_train_scaled = scaler.fit_transform(np.nan_to_num(X_features))
        joblib.dump(scaler, self.config.RESULTS_DIR / 'feature_scaler.pkl')
        logging.info(f"Příprava dat dokončena. Finální matice příznaků: {self.X_train_scaled.shape}")

    def _objective(self, trial, model_name: str):
        if model_name != 'xgboost':
            raise ValueError("Tento skript je nakonfigurován pouze pro XGBoost.")
        params = {
            'objective': 'reg:squarederror', 'eval_metric': 'mae', 'verbosity': 0,
            'n_jobs': 1, 'nthread': 1, 'random_state': self.config.RANDOM_STATE, 'tree_method': 'hist',
            'n_estimators': trial.suggest_int('n_estimators', 500, 3000, step=250),
            'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.05, log=True),
            'max_depth': trial.suggest_int('max_depth', 5, 15),
            'subsample': trial.suggest_float('subsample', 0.6, 1.0),
            'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
            'reg_alpha': trial.suggest_float('reg_alpha', 1e-3, 10.0, log=True),
            'reg_lambda': trial.suggest_float('reg_lambda', 1e-3, 10.0, log=True),
        }
        
        levels = np.array([2500, 7500, 12500, 17500])
        y_labels = levels[np.argmin(np.abs(self.y_train[:, np.newaxis] - levels), axis=1)]
        cv = StratifiedKFold(n_splits=self.config.CV_FOLDS, shuffle=True, random_state=self.config.RANDOM_STATE)
        scores = []
        
        for fold_idx, (train_idx, val_idx) in enumerate(cv.split(self.X_train_scaled, y_labels)):
            X_train_fold, X_val_fold = self.X_train_scaled[train_idx], self.X_train_scaled[val_idx]
            y_train_fold, y_val_fold = self.y_train[train_idx], self.y_train[val_idx]
            
            model = xgb.XGBRegressor(**params)
            
            model.fit(X_train_fold, y_train_fold,
                      eval_set=[(X_val_fold, y_val_fold)],
                      early_stopping_rounds=50,
                      verbose=False)

            # Bezpečné získání predikce - XGBoost automaticky použije nejlepší iteraci
            preds = model.predict(X_val_fold)
            scores.append(mean_absolute_error(y_val_fold, preds))
            
            trial.report(scores[-1], step=fold_idx)
            if trial.should_prune():
                raise optuna.exceptions.TrialPruned()
        
        return np.mean(scores)

    # --- ZDE JE VRÁCENÁ CHYBĚJÍCÍ METODA ---
    def optimize(self):
        """Spustí optimalizaci pro definované modely."""
        models_to_optimize = ['xgboost'] # Spustíme jen XGBoost
        for model_name in models_to_optimize:
            logging.info(f"\n{'='*60}\nOPTIMIZING {model_name.upper()}\n{'='*60}")
            
            study = optuna.create_study(direction='minimize', study_name=f'{model_name}_study')
            
            study.optimize(lambda trial: self._objective(trial, model_name), 
                           n_trials=self.config.N_TRIALS_PER_MODEL, 
                           n_jobs=self.config.N_JOBS_OPTUNA, 
                           show_progress_bar=True,
                           callbacks=[save_best_model_callback])
            
            self.best_params[model_name] = study.best_params
            self.best_scores[model_name] = study.best_value
            logging.info(f"Optimalizace pro {model_name} dokončena. Nejlepší MAE: {study.best_value:.2f}")

    def save_final_results(self):
        """Uloží finální souhrnné výsledky."""
        with open(self.config.RESULTS_DIR / 'best_hyperparameters_v2_final.json', 'w') as f: json.dump(self.best_params, f, indent=4)
        with open(self.config.RESULTS_DIR / 'best_scores_v2_final.json', 'w') as f: json.dump(self.best_scores, f, indent=4)
        logging.info(f"Finální výsledky optimalizace uloženy do '{self.config.RESULTS_DIR}'.")

def main():
    logger = setup_logging()
    logger.info("Spouštím finální pipeline V2 (XGBoost-only, opraveno).")
    config = Config()
    try:
        optimizer = OptunaOptimizer(config)
        optimizer.optimize()
        optimizer.save_final_results()
        
        logger.info("\n--- SHRNUTÍ OPTIMALIZACE V2 ---")
        for model, score in optimizer.best_scores.items():
            logger.info(f"Model: {model:<10} | Nejlepší MAE: {score:.2f} g")
            
    except Exception as e:
        logger.error(f"Došlo k chybě v hlavním běhu: {e}", exc_info=True)

    logger.info("Pipeline dokončen.")

if __name__ == "__main__":
    main()