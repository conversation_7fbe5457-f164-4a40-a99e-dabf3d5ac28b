"""
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, robustn<PERSON> a paralelizovaný skript pro extrakci příznaků.
Verze 2.0 - Přid<PERSON>a Envelope Analysis a Permutation Entropy.
"""
import numpy as np
from scipy import signal, stats
import pywt
from typing import List
import warnings
from joblib import Parallel, delayed
import psutil
from ordpy import permutation_entropy # Nutno nainstalovat: pip install ordpy

warnings.filterwarnings('ignore')

def extract_features_v2(sample: np.ndarray, fs: float = 1.5625e6) -> np.ndarray:
    """Extrakce pokročilých příznaků z jednoho vícekanálového vzorku."""
    all_features = []
    
    for ch_data in sample:
        # --- 1. ČASOVÁ DOMÉNA ---
        rms = np.sqrt(np.mean(ch_data**2))
        all_features.extend([
            np.mean(ch_data), np.std(ch_data), stats.skew(ch_data), stats.kurtosis(ch_data),
            np.ptp(ch_data), rms, np.max(np.abs(ch_data)) / (rms + 1e-9), np.sum(ch_data**2)
        ])
        
        # --- 2. ČASOVĚ-PROMĚNLIVÉ STATISTIKY (CHUNKING) ---
        chunks = np.array_split(ch_data, 10)
        chunk_rmss = [np.sqrt(np.mean(c**2)) for c in chunks]
        all_features.extend([np.std(chunk_rmss), np.mean([stats.kurtosis(c) for c in chunks])])

        # --- 3. FREKVENČNÍ DOMÉNA (WELCH) ---
        freqs, psd = signal.welch(ch_data, fs=fs, nperseg=512)
        psd_sum = np.sum(psd)
        all_features.extend([np.mean(psd), np.std(psd), stats.skew(psd), stats.kurtosis(psd)])
        all_features.append(np.sum(freqs * psd) / psd_sum if psd_sum > 0 else 0) # Centroid
        # Spectral Flatness
        all_features.append(stats.gmean(psd + 1e-9) / np.mean(psd) if np.mean(psd) > 0 else 0)

        # --- 4. ANALÝZA OBÁLKY (ENVELOPE ANALYSIS) - Klíčové pro ložiska ---
        analytic_signal = signal.hilbert(ch_data)
        envelope = np.abs(analytic_signal)
        env_freqs, env_psd = signal.welch(envelope, fs=fs, nperseg=512)
        all_features.extend([np.mean(env_psd), np.std(env_psd), stats.kurtosis(env_psd)])
        
        # --- 5. WAVELETOVÁ DOMÉNA ---
        try:
            coeffs = pywt.wavedec(ch_data, 'db4', level=6)
            for level_coeffs in coeffs:
                all_features.extend([np.std(level_coeffs), np.sum(level_coeffs**2)])
        except ValueError:
            all_features.extend([0] * (7 * 2))
            
        # --- 6. NELINEÁRNÍ DYNAMIKA ---
        # Permutation Entropy - robustní vůči šumu
        all_features.append(permutation_entropy(ch_data, dx=3, normalized=True))

    # --- MEZIKANÁLOVÉ PŘÍZNAKY ---
    all_features.extend(np.corrcoef(sample)[np.triu_indices(sample.shape[0], k=1)])
    channel_rmss = [np.sqrt(np.mean(ch**2)) for ch in sample]
    all_features.extend([np.mean(channel_rmss), np.std(channel_rmss)])
    
    return np.array(all_features)


class FeatureExtractorV2:
    def __init__(self, n_jobs: int = -1, fs: float = 1.5625e6):
        if n_jobs == -1: self.n_jobs = psutil.cpu_count(logical=True)
        else: self.n_jobs = n_jobs
        self.fs = fs

    def transform(self, data: np.ndarray) -> np.ndarray:
        print(f"Paralelní extrakce V2 příznaků pro {len(data)} vzorků na {self.n_jobs} jádrech...")
        features_list = Parallel(n_jobs=self.n_jobs, verbose=10)(
            delayed(extract_features_v2)(sample, self.fs) for sample in data
        )
        return np.array(features_list)