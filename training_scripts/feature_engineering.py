import numpy as np
from scipy import signal, stats
import pywt
from typing import List
import warnings
from joblib import Parallel, delayed
import psutil
import pickle
import time
import joblib

warnings.filterwarnings('ignore')

def extract_features_final(sample: np.ndarray, fs: float = 1.5625e6) -> np.ndarray:
    all_features = []
    for ch_idx, channel_data in enumerate(sample):
        # ČASOVÁ DOMÉNA
        rms = np.sqrt(np.mean(channel_data**2))
        all_features.extend([
            np.mean(channel_data), np.std(channel_data), np.var(channel_data), 
            stats.skew(channel_data), stats.kurtosis(channel_data),
            np.min(channel_data), np.max(channel_data), np.ptp(channel_data), rms,
            np.max(np.abs(channel_data)) / (rms + 1e-9),  # Crest Factor
            rms / (np.mean(np.abs(channel_data)) + 1e-9),  # Shape Factor
            np.sum(channel_data**2)  # Energy
        ])
        
        # ČASOVĚ-PROMĚNLIVÉ STATISTIKY (CHUNKING)
        n_chunks = 10
        chunks = np.array_split(channel_data, n_chunks)
        chunk_rmss = [np.sqrt(np.mean(c**2)) for c in chunks]
        chunk_kurtosis = [stats.kurtosis(c) for c in chunks]
        all_features.extend([
            np.mean(chunk_rmss), np.std(chunk_rmss),
            np.mean(chunk_kurtosis), np.std(chunk_kurtosis)
        ])
        
        # FREKVENČNÍ DOMÉNA (WELCH)
        freqs, psd = signal.welch(channel_data, fs=fs, nperseg=256)
        all_features.extend([np.mean(psd), np.std(psd), stats.skew(psd), stats.kurtosis(psd)])
        spectral_centroid = np.sum(freqs * psd) / np.sum(psd) if np.sum(psd) > 0 else 0
        all_features.append(spectral_centroid)
        band_energies = np.array_split(psd, 10)
        for band in band_energies: all_features.append(np.mean(band))

        # WAVELETOVÁ DOMÉNA
        try:
            coeffs = pywt.wavedec(channel_data, 'db4', level=6)
            for level_coeffs in coeffs:
                all_features.extend([np.mean(np.abs(level_coeffs)), np.std(level_coeffs), np.sum(level_coeffs**2)])
        except ValueError:
            all_features.extend([0] * (7 * 3))

    # MEZIKANÁLOVÉ PŘÍZNAKY
    corr_matrix = np.corrcoef(sample)
    all_features.extend(corr_matrix[np.triu_indices(sample.shape[0], k=1)])
    channel_rmss = [np.sqrt(np.mean(ch**2)) for ch in sample]
    all_features.extend([np.mean(channel_rmss), np.std(channel_rmss)])
    return np.array(all_features)


class FeatureExtractor:
    def __init__(self, n_jobs: int = -1, fs: float = 1.5625e6):
        if n_jobs == -1: self.n_jobs = psutil.cpu_count(logical=True)
        else: self.n_jobs = n_jobs
        self.fs = fs
        self.feature_names_ = None

    def _generate_feature_names(self, n_channels=4):
        if self.feature_names_ is not None: return self.feature_names_
        names = []
        for i in range(n_channels):
            names.extend([f'ch{i}_{name}' for name in ['mean', 'std', 'var', 'skew', 'kurt', 'min', 'max', 'ptp', 'rms', 'crest', 'shape', 'energy']])
            names.extend([f'ch{i}_{name}' for name in ['chunk_rms_mean', 'chunk_rms_std', 'chunk_kurt_mean', 'chunk_kurt_std']])
            names.extend([f'ch{i}_{name}' for name in ['psd_mean', 'psd_std', 'psd_skew', 'psd_kurt', 'spec_centroid']])
            names.extend([f'ch{i}_psd_band_{j}' for j in range(10)])
            for level in range(7):
                names.extend([f'ch{i}_wav_L{level}_{name}' for name in ['abs_mean', 'std', 'energy']])
        for i in range(n_channels):
            for j in range(i + 1, n_channels): names.append(f'corr_ch{i}_ch{j}')
        names.extend(['cross_ch_rms_mean', 'cross_ch_rms_std'])
        self.feature_names_ = names
        return names

    def transform(self, data: np.ndarray) -> np.ndarray:
        print(f"Paralelní extrakce příznaků pro {len(data)} vzorků na {self.n_jobs} jádrech...")
        features_list = Parallel(n_jobs=self.n_jobs, verbose=10)(
            delayed(extract_features_final)(sample, self.fs) for sample in data
        )
        features_array = np.array(features_list)
        if self.feature_names_ is None and data.shape[0] > 0:
            self._generate_feature_names(data.shape[1])
            print(f"Extrakce dokončena. Celkem {len(self.feature_names_)} příznaků.")
        return features_array
    
    def get_feature_names(self) -> List[str]:
        if self.feature_names_ is None: self._generate_feature_names()
        return self.feature_names_


def test_feature_extraction():
    """Testuje a ukazuje použití vylepšené extrakce příznaků."""
    try:
        with open('student_data.pkl', 'rb') as f:
            data = pickle.load(f)
        train_data = data['train_dataset']
    except FileNotFoundError:
        print("Testovací soubor 'student_data.pkl' nenalezen. Test bude přeskočen.")
        return

    # Vytvoření extraktoru s využitím 16 jader
    extractor = FeatureExtractor(n_jobs=16)
    
    # Test na prvních 100 vzorcích pro rychlost
    print("\n--- Testování na 100 vzorcích ---")
    start_time = time.time()
    test_features = extractor.transform(train_data[:100])
    end_time = time.time()
    
    print(f"\nZpracování 100 vzorků trvalo: {end_time - start_time:.2f} s")
    print(f"Tvar výsledných příznaků: {test_features.shape}")
    
    feature_names = extractor.get_feature_names()
    print(f"Celkový počet příznaků: {len(feature_names)}")
    
    print("\nUkázka prvních 10 názvů příznaků:")
    for i, name in enumerate(feature_names[:10]):
        print(f"  {i+1}. {name}")
        
    print("\nUkázka posledních 10 názvů příznaků:")
    for i, name in enumerate(feature_names[-10:]):
        print(f"  {len(feature_names)-10+i+1}. {name}")
    
    # Uložení extraktoru pro pozdější použití
    joblib.dump(extractor, 'feature_extractor.pkl')
    print("\nExtraktor uložen do 'feature_extractor.pkl'")

if __name__ == "__main__":
    # Tento blok se spustí, když zavoláte `python feature_engineering.py`
    # Je ideální pro testování, zda vše funguje.
    test_feature_extraction()