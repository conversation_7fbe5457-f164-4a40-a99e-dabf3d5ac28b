"""
Finální skript pro trénování ensemble modelu, generování predikcí a validaci.
Verze 7.1 - Trénuje XGBoost i LightGBM, vytváří ensemble a testuje MAE na původních datech.
"""
import numpy as np
import pickle
import joblib
import time
import logging
import sys
from pathlib import Path
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# ML knihovny
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import mean_absolute_error
from sklearn.base import BaseEstimator, TransformerMixin
import xgboost as xgb
import lightgbm as lgb

# Import extraktoru
try:
    sys.path.append('training_scripts')
    from feature_engineering_v2 import FeatureExtractorV2
except ImportError:
    print("CHYBA: Ujistěte se, že máte soubor 'training_scripts/feature_engineering_v2.py'.")
    sys.exit(1)

def setup_logging():
    log_dir = Path("logs"); log_dir.mkdir(exist_ok=True)
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"final_ensemble_run_{timestamp}.log"
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s',
                        handlers=[logging.FileHandler(log_file), logging.StreamHandler(sys.stdout)])
    return logging.getLogger(__name__)

# --- Zde vložte třídy Config a SignalAugmenter z předchozího skriptu ---
class Config:
    DATA_PATH = '/home/<USER>/Loziska/AMSM_LOZISKA_2025/student_data.pkl'
    RESULTS_DIR = Path('final_submission')
    RANDOM_STATE = 42
    N_JOBS_EXTRACTION = 16
    NUM_SYNTHETIC_SAMPLES = 40000

class SignalAugmenter:
    def __init__(self, n_synthetic, discrete_levels=None, noise_level=0.01):
        self.n_synthetic, self.discrete_levels, self.noise_level = n_synthetic, discrete_levels or [2500, 7500, 12500, 17500], noise_level
    def fit_resample(self, X, y):
        logging.info(f"Generuji {self.n_synthetic} syntetických signálů...")
        X_synth, y_synth = [], []
        level_indices = {level: np.where(y == level)[0] for level in self.discrete_levels}
        for _ in tqdm(range(self.n_synthetic), desc="Generování signálů"):
            level_idx = np.random.randint(0, len(self.discrete_levels) - 1)
            level1, level2 = self.discrete_levels[level_idx], self.discrete_levels[level_idx+1]
            idx1, idx2 = np.random.choice(level_indices[level1]), np.random.choice(level_indices[level2])
            signal1, signal2 = X[idx1], X[idx2]
            alpha = np.random.rand()
            new_signal = (1 - alpha) * signal1 + alpha * signal2
            new_y = (1 - alpha) * level1 + alpha * level2
            noise = np.random.normal(0, self.noise_level * np.std(new_signal), new_signal.shape)
            X_synth.append(new_signal + noise); y_synth.append(new_y)
        X_final = np.vstack([X, np.array(X_synth)])
        y_final = np.concatenate([y, np.array(y_synth)])
        logging.info(f"Augmentace dokončena. Celkem {len(X_final)} vzorků.")
        return X_final, y_final

def main():
    logger = setup_logging()
    logger.info("="*70 + "\nFINÁLNÍ TRÉNOVÁNÍ ENSEMBLU A PREDIKCE\n" + "="*70)
    config = Config()
    
    try:
        # --- 1. Příprava dat ---
        logger.info("Načítání a příprava dat...")
        with open(config.DATA_PATH, 'rb') as f: data = pickle.load(f)
        X_train_raw, y_train_raw = data['train_dataset'], data['train_response']
        X_test_raw = data['test_dataset']
        
        augmenter = SignalAugmenter(n_synthetic=config.NUM_SYNTHETIC_SAMPLES)
        X_aug_raw, y_aug_train = augmenter.fit_resample(X_train_raw, y_train_raw)
        
        logger.info("Extrakce V2 příznaků...")
        feature_extractor = FeatureExtractorV2(n_jobs=config.N_JOBS_EXTRACTION)
        
        logger.info("-> Zpracovávám původní trénovací data (pro validaci)...")
        X_train_features = feature_extractor.transform(X_train_raw)
        X_train_features = np.nan_to_num(X_train_features)

        logger.info("-> Zpracovávám augmentovaná trénovací data...")
        X_aug_features = feature_extractor.transform(X_aug_raw)
        X_aug_features = np.nan_to_num(X_aug_features)

        logger.info("-> Zpracovávám testovací data...")
        X_test_features = feature_extractor.transform(X_test_raw)
        X_test_features = np.nan_to_num(X_test_features)
        
        logger.info("Škálování dat...")
        scaler = RobustScaler()
        X_aug_scaled = scaler.fit_transform(X_aug_features)
        X_train_scaled = scaler.transform(X_train_features) # Použijeme scaler natrénovaný na augmentovaných datech
        X_test_scaled = scaler.transform(X_test_features)

        # --- 2. Definice a trénování finálních modelů ---
        # Nejlepší parametry z vašeho Optuna běhu
        best_xgb_params = {
            'objective': 'reg:squarederror', 'eval_metric': 'mae', 'verbosity': 1,
            'n_jobs': -1, 'random_state': config.RANDOM_STATE, 'tree_method': 'hist',
            'n_estimators': 2750, 'learning_rate': 0.023117, 'max_depth': 9,
            'subsample': 0.66157, 'colsample_bytree': 0.95148,
            'reg_alpha': 1.5572, 'reg_lambda': 4.2016
        }
        best_lgbm_params = {
            'objective': 'regression_l1', 'metric': 'mae', 'verbosity': -1,
            'random_state': config.RANDOM_STATE, 'n_jobs': -1,
            'n_estimators': 3750, 'learning_rate': 0.04229, 'max_depth': 13,
            'num_leaves': 2924
        }
        
        # Trénování XGBoost
        logger.info("\n--- Trénování finálního XGBoost modelu ---")
        xgb_model = xgb.XGBRegressor(**best_xgb_params)
        start_time = time.time()
        xgb_model.fit(X_aug_scaled, y_aug_train)
        logger.info(f"Trénování XGBoost dokončeno za {time.time() - start_time:.1f}s.")
        
        # Trénování LightGBM
        logger.info("\n--- Trénování finálního LightGBM modelu ---")
        lgbm_model = lgb.LGBMRegressor(**best_lgbm_params)
        start_time = time.time()
        lgbm_model.fit(X_aug_scaled, y_aug_train)
        logger.info(f"Trénování LightGBM dokončeno za {time.time() - start_time:.1f}s.")
        
        # --- 3. Validace na původních datech ---
        logger.info("\n--- Validace na PŮVODNÍCH (neaugmentovaných) trénovacích datech ---")
        
        xgb_preds_train = xgb_model.predict(X_train_scaled)
        lgbm_preds_train = lgbm_model.predict(X_train_scaled)
        
        # Průměrovaný ensemble
        ensemble_preds_train = (xgb_preds_train + lgbm_preds_train) / 2
        
        train_mae = mean_absolute_error(y_train_raw, ensemble_preds_train)
        logger.info(f"MAE finálního ensemblu na původních datech: {train_mae:.4f} g")
        
        if train_mae < 100:
            logger.info("Výsledek je vynikající! Model si téměř dokonale 'pamatuje' trénovací body.")
        else:
            logger.warning("MAE na trénovacích datech není nulová. To je očekávané kvůli šumu a regularizaci, která brání přeučení.")

        # --- 4. Finální predikce a Ensemble ---
        logger.info("\n--- Generování finálních predikcí na testovacích datech ---")
        xgb_preds_test = xgb_model.predict(X_test_scaled)
        lgbm_preds_test = lgbm_model.predict(X_test_scaled)
        
        # Jednoduchý průměrovací ensemble
        ensemble_predictions = (xgb_preds_test + lgbm_preds_test) / 2
        final_predictions = np.clip(ensemble_predictions, 0, 19200)

        # --- 5. Uložení výsledků ---
        config.RESULTS_DIR.mkdir(exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        preds_path = config.RESULTS_DIR / f"predictions_ensemble_{timestamp}.npy"
        np.save(preds_path, final_predictions)
        joblib.dump(xgb_model, config.RESULTS_DIR / f"xgb_model_{timestamp}.pkl")
        joblib.dump(lgbm_model, config.RESULTS_DIR / f"lgbm_model_{timestamp}.pkl")
        joblib.dump(scaler, config.RESULTS_DIR / f"scaler_{timestamp}.pkl")
        
        # Pro snadné odevzdání
        np.save('predictions.npy', final_predictions)
        logger.info(f"Finální predikce a modely uloženy do: {config.RESULTS_DIR}")
        
    except Exception as e:
        logger.error(f"Došlo k fatální chybě: {e}", exc_info=True)

    logger.info("Pipeline dokončen.")

if __name__ == "__main__":
    main()