import numpy as np
import pandas as pd
from pathlib import Path

# --- NASTAVENÍ ---
# Cesta k vašemu finálnímu souboru s predikcemi
# Skript předpoklád<PERSON>, že soubor 'predictions.npy' je ve stejné složce
INPUT_NPY_FILE = '/home/<USER>/Loziska/final_submission/predictions_xgb_only_20250727_105802.npy'

# Název výstupního souboru pro odevzdání
OUTPUT_TXT_FILE = 'prusek_michal_predictions.txt'
OUTPUT_CSV_FILE = 'prusek_michal_predictions.csv'

# --- SKRIPT ---

def create_submission_files(input_path, output_txt, output_csv):
    """
    Načte predikce z .npy souboru a uloží je do .txt a .csv formátu.
    """
    input_file = Path(input_path)
    if not input_file.exists():
        print(f"CHYBA: Vstupn<PERSON> soubor '{input_path}' nebyl nalezen!")
        return

    try:
        # Načtení predikcí
        predictions = np.load(input_file)
        print(f"Úspěšně načteno {len(predictions)} predikcí z '{input_path}'.")

        if len(predictions) != 3200:
            print(f"VAROVÁNÍ: Počet predikcí ({len(predictions)}) není 3200!")

        # --- Metoda A: Uložení do .txt (DOPORUČENO) ---
        # Uloží soubor s jedním číslem na řádek, s přesností na 4 desetinná místa
        np.savetxt(output_txt, predictions, fmt='%.4f')
        print(f"Predikce úspěšně uloženy do doporučeného formátu: '{output_txt}'")

        # --- Metoda B: Uložení do .csv (také velmi dobrá volba) ---
        df = pd.DataFrame({'prediction_g': predictions})
        df.to_csv(output_csv, index=False, header=True)
        print(f"Predikce úspěšně uloženy do alternativního formátu: '{output_csv}'")
        
        print("\nHotovo! Můžete odevzdat soubor " + output_txt + " nebo " + output_csv)

    except Exception as e:
        print(f"Došlo k chybě: {e}")


if __name__ == "__main__":
    create_submission_files(INPUT_NPY_FILE, OUTPUT_TXT_FILE, OUTPUT_CSV_FILE)