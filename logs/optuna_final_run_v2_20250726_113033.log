2025-07-26 11:30:33,877 - INFO - Spouštím finální pipeline V2 (Signal Augmentation).
2025-07-26 11:30:33,877 - INFO - Nač<PERSON>tání a příprava dat...
2025-07-26 11:30:35,843 - INFO - Generuji 40000 syntetických signálů...
2025-07-26 11:30:54,512 - INFO - Augmentace dokončena. Celkem 46416 vzorků.
2025-07-26 11:30:54,532 - INFO - Extrakce V2 příznaků z augmentovaných dat...
2025-07-26 11:33:20,513 - INFO - Příprava dat dokončena. Finální matice příznaků: (46416, 144)
2025-07-26 11:33:20,532 - INFO - 
============================================================
OPTIMIZING LIGHTGBM
============================================================
2025-07-26 11:35:11,771 - INFO - Nový nejlepší model pro 'lightgbm' nalezen! MAE: 1532.71. Uloženo do optuna_results_final_v2/best_params_lightgbm.json
2025-07-26 11:36:56,424 - INFO - Nový nejlepší model pro 'lightgbm' nalezen! MAE: 1383.64. Uloženo do optuna_results_final_v2/best_params_lightgbm.json
2025-07-26 11:39:14,379 - INFO - Nový nejlepší model pro 'lightgbm' nalezen! MAE: 1294.50. Uloženo do optuna_results_final_v2/best_params_lightgbm.json
2025-07-26 11:48:44,724 - INFO - Nový nejlepší model pro 'lightgbm' nalezen! MAE: 1277.04. Uloženo do optuna_results_final_v2/best_params_lightgbm.json
2025-07-26 11:50:49,604 - INFO - Nový nejlepší model pro 'lightgbm' nalezen! MAE: 1271.51. Uloženo do optuna_results_final_v2/best_params_lightgbm.json
2025-07-26 11:55:52,973 - INFO - Nový nejlepší model pro 'lightgbm' nalezen! MAE: 1246.91. Uloženo do optuna_results_final_v2/best_params_lightgbm.json
2025-07-26 11:58:51,877 - INFO - Nový nejlepší model pro 'lightgbm' nalezen! MAE: 1237.00. Uloženo do optuna_results_final_v2/best_params_lightgbm.json
2025-07-26 12:20:37,197 - INFO - Nový nejlepší model pro 'lightgbm' nalezen! MAE: 1230.09. Uloženo do optuna_results_final_v2/best_params_lightgbm.json
2025-07-26 12:21:27,446 - INFO - Nový nejlepší model pro 'lightgbm' nalezen! MAE: 1229.93. Uloženo do optuna_results_final_v2/best_params_lightgbm.json
2025-07-26 13:16:36,468 - INFO - Nový nejlepší model pro 'lightgbm' nalezen! MAE: 1228.02. Uloženo do optuna_results_final_v2/best_params_lightgbm.json
2025-07-26 14:19:38,668 - INFO - Optimalizace pro lightgbm dokončena. Nejlepší MAE: 1228.02
2025-07-26 14:19:38,668 - INFO - 
============================================================
OPTIMIZING XGBOOST
============================================================
2025-07-26 14:19:38,805 - ERROR - Došlo k chybě v hlavním běhu: fit() got an unexpected keyword argument 'early_stopping_rounds'
Traceback (most recent call last):
  File "/home/<USER>/Loziska/pipeline_v2.py", line 206, in main
    optimizer.optimize()
  File "/home/<USER>/Loziska/pipeline_v2.py", line 184, in optimize
    study.optimize(lambda trial: self._objective(trial, model_name),
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/study.py", line 489, in optimize
    _optimize(
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 101, in _optimize
    f.result()
  File "/usr/lib64/python3.9/concurrent/futures/_base.py", line 439, in result
    return self.__get_result()
  File "/usr/lib64/python3.9/concurrent/futures/_base.py", line 391, in __get_result
    raise self._exception
  File "/usr/lib64/python3.9/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 161, in _optimize_sequential
    frozen_trial = _run_trial(study, func, catch)
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 253, in _run_trial
    raise func_err
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 201, in _run_trial
    value_or_values = func(trial)
  File "/home/<USER>/Loziska/pipeline_v2.py", line 184, in <lambda>
    study.optimize(lambda trial: self._objective(trial, model_name),
  File "/home/<USER>/Loziska/pipeline_v2.py", line 159, in _objective
    model.fit(X_train_fold, y_train_fold,
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/xgboost/core.py", line 726, in inner_f
    return func(**kwargs)
TypeError: fit() got an unexpected keyword argument 'early_stopping_rounds'
2025-07-26 14:19:38,806 - INFO - Pipeline dokončen.
