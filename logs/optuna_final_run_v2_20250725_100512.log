2025-07-25 10:05:12,957 - INFO - Spouštím finální pipeline V2 (Signal Augmentation).
2025-07-25 10:05:12,957 - INFO - Načítání a příprava dat...
2025-07-25 10:05:13,377 - INFO - Generuji 40000 syntetických signálů...
2025-07-25 10:05:32,338 - INFO - Augmentace dokončena. Celkem 46416 vzorků.
2025-07-25 10:05:32,361 - INFO - Extrakce V2 příznaků z augmentovaných dat...
2025-07-25 10:05:34,650 - ERROR - Došlo k chybě v hlavním běhu: permutation_entropy() got an unexpected keyword argument 'tau'
joblib.externals.loky.process_executor._RemoteTraceback: 
"""
Traceback (most recent call last):
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/joblib/externals/loky/process_executor.py", line 490, in _process_worker
    r = call_item()
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/joblib/externals/loky/process_executor.py", line 291, in __call__
    return self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/joblib/parallel.py", line 607, in __call__
    return [func(*args, **kwargs) for func, args, kwargs in self.items]
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/joblib/parallel.py", line 607, in <listcomp>
    return [func(*args, **kwargs) for func, args, kwargs in self.items]
  File "/home/<USER>/Loziska/training_scripts/feature_engineering_v2.py", line 57, in extract_features_v2
    all_features.append(permutation_entropy(ch_data, dx=3, tau=1, normalized=True))
TypeError: permutation_entropy() got an unexpected keyword argument 'tau'
"""

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Loziska/pipeline_v2.py", line 183, in main
    optimizer = OptunaOptimizer(config)
  File "/home/<USER>/Loziska/pipeline_v2.py", line 97, in __init__
    self._load_and_prepare_data()
  File "/home/<USER>/Loziska/pipeline_v2.py", line 109, in _load_and_prepare_data
    X_features = self.feature_extractor.transform(X_aug_raw)
  File "/home/<USER>/Loziska/training_scripts/feature_engineering_v2.py", line 75, in transform
    features_list = Parallel(n_jobs=self.n_jobs, verbose=10)(
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/joblib/parallel.py", line 2072, in __call__
    return output if self.return_generator else list(output)
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/joblib/parallel.py", line 1682, in _get_outputs
    yield from self._retrieve()
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/joblib/parallel.py", line 1784, in _retrieve
    self._raise_error_fast()
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/joblib/parallel.py", line 1859, in _raise_error_fast
    error_job.get_result(self.timeout)
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/joblib/parallel.py", line 758, in get_result
    return self._return_or_raise()
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/joblib/parallel.py", line 773, in _return_or_raise
    raise self._result
TypeError: permutation_entropy() got an unexpected keyword argument 'tau'
2025-07-25 10:05:34,733 - INFO - Pipeline dokončen.
