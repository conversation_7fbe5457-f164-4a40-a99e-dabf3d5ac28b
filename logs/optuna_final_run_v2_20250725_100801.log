2025-07-25 10:08:01,652 - INFO - Spouštím finální pipeline V2 (Signal Augmentation).
2025-07-25 10:08:01,652 - INFO - Načítání a příprava dat...
2025-07-25 10:08:02,038 - INFO - Generuji 40000 syntetických signálů...
2025-07-25 10:08:21,105 - INFO - Augmentace dokončena. Celkem 46416 vzorků.
2025-07-25 10:08:21,127 - INFO - Extrakce V2 příznaků z augmentovaných dat...
2025-07-25 10:10:48,933 - INFO - Příprava dat dokončena. Finální matice příznaků: (46416, 144)
2025-07-25 10:10:48,953 - INFO - 
============================================================
OPTIMIZING LIGHTGBM
============================================================
2025-07-25 12:36:21,760 - INFO - Optimalizace pro lightgbm dokončena. Nejlepší MAE: 1237.17
2025-07-25 12:36:21,760 - INFO - 
============================================================
OPTIMIZING XGBOOST
============================================================
2025-07-25 12:36:22,006 - ERROR - Došlo k chybě v hlavním běhu: fit() got an unexpected keyword argument 'callbacks'
Traceback (most recent call last):
  File "/home/<USER>/Loziska/pipeline_v2.py", line 184, in main
    optimizer.optimize()
  File "/home/<USER>/Loziska/pipeline_v2.py", line 167, in optimize
    study.optimize(lambda trial: self._objective(trial, model_name),
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/study.py", line 489, in optimize
    _optimize(
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 101, in _optimize
    f.result()
  File "/usr/lib64/python3.9/concurrent/futures/_base.py", line 439, in result
    return self.__get_result()
  File "/usr/lib64/python3.9/concurrent/futures/_base.py", line 391, in __get_result
    raise self._exception
  File "/usr/lib64/python3.9/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 161, in _optimize_sequential
    frozen_trial = _run_trial(study, func, catch)
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 253, in _run_trial
    raise func_err
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 201, in _run_trial
    value_or_values = func(trial)
  File "/home/<USER>/Loziska/pipeline_v2.py", line 167, in <lambda>
    study.optimize(lambda trial: self._objective(trial, model_name),
  File "/home/<USER>/Loziska/pipeline_v2.py", line 153, in _objective
    model.fit(X_train_fold, y_train_fold, eval_set=[(X_val_fold, y_val_fold)], callbacks=callbacks)
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/xgboost/core.py", line 726, in inner_f
    return func(**kwargs)
TypeError: fit() got an unexpected keyword argument 'callbacks'
2025-07-25 12:36:22,007 - INFO - Pipeline dokončen.
