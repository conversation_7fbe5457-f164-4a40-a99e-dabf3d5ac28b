2025-07-26 14:24:27,314 - INFO - Spouštím finální pipeline V2 (XGBoost-only).
2025-07-26 14:24:27,314 - INFO - Načítání a příprava dat...
2025-07-26 14:24:27,754 - INFO - Generuji 40000 syntetických signálů...
2025-07-26 14:24:47,461 - INFO - Augmentace dokončena. Celkem 46416 vzorků.
2025-07-26 14:24:47,488 - INFO - Extrakce V2 příznaků z augmentovaných dat...
2025-07-26 14:27:13,625 - INFO - Příprava dat dokončena. Finální matice příznaků: (46416, 144)
2025-07-26 14:27:13,644 - INFO - 
============================================================
OPTIMIZING XGBOOST
============================================================
2025-07-26 14:27:13,711 - ERROR - Došlo k chybě v hlavním běhu: fit() got an unexpected keyword argument 'callbacks'
Traceback (most recent call last):
  File "/home/<USER>/Loziska/pipeline_v2_xgboost.py", line 183, in main
    optimizer.optimize()
  File "/home/<USER>/Loziska/pipeline_v2_xgboost.py", line 162, in optimize
    study.optimize(lambda trial: self._objective(trial, model_name),
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/study.py", line 489, in optimize
    _optimize(
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 101, in _optimize
    f.result()
  File "/usr/lib64/python3.9/concurrent/futures/_base.py", line 439, in result
    return self.__get_result()
  File "/usr/lib64/python3.9/concurrent/futures/_base.py", line 391, in __get_result
    raise self._exception
  File "/usr/lib64/python3.9/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 161, in _optimize_sequential
    frozen_trial = _run_trial(study, func, catch)
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 253, in _run_trial
    raise func_err
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 201, in _run_trial
    value_or_values = func(trial)
  File "/home/<USER>/Loziska/pipeline_v2_xgboost.py", line 162, in <lambda>
    study.optimize(lambda trial: self._objective(trial, model_name),
  File "/home/<USER>/Loziska/pipeline_v2_xgboost.py", line 142, in _objective
    model.fit(X_train_fold, y_train_fold,
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/xgboost/core.py", line 726, in inner_f
    return func(**kwargs)
TypeError: fit() got an unexpected keyword argument 'callbacks'
2025-07-26 14:27:13,712 - INFO - Pipeline dokončen.
