2025-07-25 12:59:15,944 - INFO - Spouštím finální pipeline V2 (Signal Augmentation).
2025-07-25 12:59:15,944 - INFO - Načítání a příprava dat...
2025-07-25 12:59:16,410 - INFO - Generuji 40000 syntetických signálů...
2025-07-25 12:59:35,554 - INFO - Augmentace dokončena. Celkem 46416 vzorků.
2025-07-25 12:59:35,579 - INFO - Extrakce V2 příznaků z augmentovaných dat...
2025-07-25 13:02:05,999 - INFO - Příprava dat dokončena. Finální matice příznaků: (46416, 144)
2025-07-25 13:02:06,017 - INFO - 
============================================================
OPTIMIZING LIGHTGBM
============================================================
2025-07-25 16:03:01,378 - INFO - Optimalizace pro lightgbm dokončena. Nejlepší MAE: 1238.27
2025-07-25 16:03:01,378 - INFO - 
============================================================
OPTIMIZING XGBOOST
============================================================
2025-07-25 16:03:02,108 - ERROR - Došlo k chybě v hlavním běhu: fit() got an unexpected keyword argument 'early_stopping_rounds'
Traceback (most recent call last):
  File "/home/<USER>/Loziska/pipeline_v2.py", line 180, in main
    optimizer.optimize()
  File "/home/<USER>/Loziska/pipeline_v2.py", line 163, in optimize
    study.optimize(lambda trial: self._objective(trial, model_name),
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/study.py", line 489, in optimize
    _optimize(
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 101, in _optimize
    f.result()
  File "/usr/lib64/python3.9/concurrent/futures/_base.py", line 439, in result
    return self.__get_result()
  File "/usr/lib64/python3.9/concurrent/futures/_base.py", line 391, in __get_result
    raise self._exception
  File "/usr/lib64/python3.9/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 161, in _optimize_sequential
    frozen_trial = _run_trial(study, func, catch)
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 253, in _run_trial
    raise func_err
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 201, in _run_trial
    value_or_values = func(trial)
  File "/home/<USER>/Loziska/pipeline_v2.py", line 163, in <lambda>
    study.optimize(lambda trial: self._objective(trial, model_name),
  File "/home/<USER>/Loziska/pipeline_v2.py", line 139, in _objective
    model.fit(X_train_fold, y_train_fold,
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/xgboost/core.py", line 726, in inner_f
    return func(**kwargs)
TypeError: fit() got an unexpected keyword argument 'early_stopping_rounds'
2025-07-25 16:03:02,111 - INFO - Pipeline dokončen.
