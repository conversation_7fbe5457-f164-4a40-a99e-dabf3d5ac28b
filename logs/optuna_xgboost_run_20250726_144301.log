2025-07-26 14:43:01,347 - INFO - Spouštím finální pipeline V2 (XGBoost-only, opraveno).
2025-07-26 14:43:01,347 - INFO - Načítání a příprava dat...
2025-07-26 14:43:01,763 - INFO - Generuji 40000 syntetických signálů...
2025-07-26 14:43:20,798 - INFO - Augmentace dokončena. Celkem 46416 vzorků.
2025-07-26 14:43:20,821 - INFO - Extrakce V2 příznaků z augmentovaných dat...
2025-07-26 14:45:48,933 - INFO - Příprava dat dokončena. Finální matice příznaků: (46416, 144)
2025-07-26 14:45:48,953 - INFO - 
============================================================
OPTIMIZING XGBOOST
============================================================
2025-07-26 14:45:48,980 - ERROR - Došlo k chybě v hlavním běhu: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "/home/<USER>/Loziska/pipeline_v2_xgboost.py", line 178, in main
    optimizer.optimize()
  File "/home/<USER>/Loziska/pipeline_v2_xgboost.py", line 158, in optimize
    study.optimize(lambda trial: self._objective(trial, model_name),
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/study.py", line 489, in optimize
    _optimize(
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 101, in _optimize
    f.result()
  File "/usr/lib64/python3.9/concurrent/futures/_base.py", line 439, in result
    return self.__get_result()
  File "/usr/lib64/python3.9/concurrent/futures/_base.py", line 391, in __get_result
    raise self._exception
  File "/usr/lib64/python3.9/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 161, in _optimize_sequential
    frozen_trial = _run_trial(study, func, catch)
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 253, in _run_trial
    raise func_err
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/optuna/study/_optimize.py", line 201, in _run_trial
    value_or_values = func(trial)
  File "/home/<USER>/Loziska/pipeline_v2_xgboost.py", line 158, in <lambda>
    study.optimize(lambda trial: self._objective(trial, model_name),
  File "/home/<USER>/Loziska/pipeline_v2_xgboost.py", line 127, in _objective
    for fold, (train_idx, val_idx) in cv.split(self.X_train_scaled, y_labels):
ValueError: too many values to unpack (expected 2)
2025-07-26 14:45:48,981 - INFO - Pipeline dokončen.
